'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  School, Mail, Phone, MapPin, Hash, Edit, Building, UserCircle, Briefcase, Tag,
  Users, GraduationCap, ArrowLeft, ExternalLink, Calendar, Clock, Info, BookOpen,
  Award, Home, Palette, Trash2, Alert<PERSON>riangle,
} from 'lucide-react';
import { InfoField } from '@/components/molecules/InfoField/InfoField';
import { ISchoolResponse } from '@/apis/schoolApi';
import { Button } from '@/components/atoms/Button/Button';
import { getFileRenderUrl } from '@/utils/fileUtils';

import { EUserRole } from '@/config/enums/user';

export interface SchoolDetailCardProps {
  school: ISchoolResponse;
  onEdit?: (school: ISchoolResponse) => void;
  onDelete?: (schoolId: string) => Promise<void>;
  canDelete?: boolean;
  userRole?: EUserRole;
}

export const SchoolDetailCard: React.FC<SchoolDetailCardProps> = ({
  school,
  onEdit,
  onDelete,
  canDelete = false,
  userRole,
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleEditClick = () => {
    if (onEdit) {
      onEdit(school);
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    if (onDelete) {
      try {
        setIsDeleting(true);
        await onDelete(school.id);
        // The parent component should handle navigation after successful deletion
      } catch (error) {
        console.error('Error deleting school:', error);
        setIsDeleting(false);
        setShowDeleteConfirm(false);
      }
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  // Determine brand color for styling elements
  const brandColor = school.brand?.color || '#3B82F6';
  const avatarGradient = `from-[${brandColor}] to-[${brandColor}]/80`;

  // Format phone number for better readability
  const formatPhoneNumber = (phone: string) => {
    // Simple formatting - could be enhanced based on specific requirements
    return phone.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
  };

  return (
    <div className="relative group">
      {/* AI-Enhanced Background with Animated Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/80 via-purple-50/60 to-indigo-50/80 rounded-2xl blur-xl opacity-60 group-hover:opacity-80 transition-all duration-700"></div>
      
      {/* Main Card with Glassmorphism */}
      <div className="relative bg-white/90 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden transition-all duration-500 hover:shadow-2xl hover:scale-[1.02] hover:bg-white/95">
        {/* Floating Particles Animation */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-4 left-4 w-2 h-2 bg-blue-400/30 rounded-full animate-pulse"></div>
          <div className="absolute top-8 right-8 w-1 h-1 bg-purple-400/40 rounded-full animate-ping"></div>
          <div className="absolute bottom-6 left-6 w-1.5 h-1.5 bg-indigo-400/30 rounded-full animate-bounce"></div>
        </div>

        {/* Delete confirmation dialog with enhanced AI styling */}
        {showDeleteConfirm && (
          <div className="absolute inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl max-w-md w-full p-8 border border-white/20 animate-fade-in">
              {/* AI-Enhanced Header */}
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-orange-500/10 rounded-xl blur-lg"></div>
                <div className="relative flex items-center text-red-600 bg-red-50/80 backdrop-blur-sm rounded-xl p-4">
                  <div className="relative">
                    <AlertTriangle size={24} className="mr-3" />
                    <div className="absolute inset-0 bg-red-500/20 rounded-full blur-md animate-pulse"></div>
                  </div>
                  <h3 className="text-lg font-semibold">AI Deletion Confirmation</h3>
                </div>
              </div>
              
              <div className="space-y-4 mb-8">
                <p className="text-gray-700 font-medium">
                  Are you sure you want to delete <strong className="text-red-600">{school.name}</strong>?
                </p>
                <div className="bg-amber-50/80 backdrop-blur-sm border border-amber-200/50 rounded-xl p-4">
                  <p className="text-amber-800 text-sm font-medium flex items-center gap-2">
                    <AlertTriangle size={16} className="text-amber-600" />
                    This action cannot be undone
                  </p>
                  <p className="text-amber-700 text-sm mt-1">
                    All data associated with this school will be permanently removed from our AI systems.
                  </p>
                </div>
              </div>
              
              <div className="flex justify-end gap-4">
                <Button
                  variant="outline"
                  onClick={handleCancelDelete}
                  disabled={isDeleting}
                  className="text-sm bg-white/80 backdrop-blur-sm hover:bg-white/90 border-gray-200/50"
                >
                  Cancel
                </Button>
                <Button
                  variant="error"
                  onClick={handleConfirmDelete}
                  disabled={isDeleting}
                  className="relative bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white text-sm flex items-center gap-2 overflow-hidden"
                >
                  {/* Animated Background */}
                  <div className="absolute inset-0 bg-gradient-to-r from-red-500/20 to-orange-500/20 animate-pulse"></div>
                  
                  {isDeleting ? (
                    <div className="relative flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <span className="font-medium">AI Processing...</span>
                    </div>
                  ) : (
                    <div className="relative flex items-center gap-2">
                      <Trash2 size={16} className="animate-pulse" />
                      <span className="font-medium">Confirm Deletion</span>
                    </div>
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* AI-Enhanced Header with Advanced Styling */}
        <div className="relative bg-gradient-to-r from-blue-50/80 via-purple-50/60 to-indigo-50/80 backdrop-blur-sm border-b border-white/30 px-8 py-6">
          {/* Animated Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-0 left-1/4 w-32 h-32 bg-blue-400/10 rounded-full blur-2xl animate-pulse"></div>
            <div className="absolute bottom-0 right-1/4 w-24 h-24 bg-purple-400/10 rounded-full blur-xl animate-bounce"></div>
          </div>
          
          <div className="relative flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Enhanced Status Indicator */}
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="w-4 h-4 bg-green-500 rounded-full animate-pulse shadow-lg"></div>
                  <div className="absolute inset-0 w-4 h-4 bg-green-400/50 rounded-full animate-ping"></div>
                </div>
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 backdrop-blur-sm px-4 py-2 rounded-full border border-green-200/50">
                  <span className="text-sm font-semibold text-green-700 flex items-center gap-2">
                    <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                    AI-Managed Active School
                  </span>
                </div>
              </div>
              
              {/* Enhanced Registration Number */}
              {school.registeredNumber && (
                <div className="bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-300">
                  <span className="text-sm text-gray-700">
                    <span className="font-semibold text-blue-600">Reg:</span> 
                    <span className="font-mono ml-1">{school.registeredNumber}</span>
                  </span>
                </div>
              )}
            </div>
            
            {/* Enhanced Metadata Section */}
            <div className="flex items-center gap-6 text-xs text-gray-600">
              {school.createdAt && (
                <div className="flex items-center gap-2 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-full border border-gray-200/30">
                  <Calendar size={14} className="text-blue-500" />
                  <span className="font-medium">Est. {new Date(school.createdAt).toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'short', 
                    day: 'numeric' 
                  })}</span>
                </div>
              )}
              {school.updatedAt && school.updatedAt !== school.createdAt && (
                <div className="flex items-center gap-2 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-full border border-gray-200/30">
                  <Clock size={14} className="text-purple-500" />
                  <span className="font-medium">Updated {new Date(school.updatedAt).toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'short', 
                    day: 'numeric' 
                  })}</span>
                </div>
              )}
            </div>
          </div>
        </div>

      <div className="p-8">
        {/* AI-Enhanced School Header with Brand Integration */}
        <div className="flex flex-col gap-8 mb-10">
          {/* School header with integrated logo and name - AI Enhanced */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              {/* Enhanced School Logo/Brand with AI Effects */}
              <div className="flex-shrink-0 relative group">
                {/* Animated Rings */}
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-indigo-400/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                <div className="absolute -inset-2 bg-gradient-to-r from-blue-400/10 via-purple-400/10 to-indigo-400/10 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse"></div>
                
                {school.brand?.logo ? (
                  <div className="relative w-20 h-20 rounded-2xl overflow-hidden border-2 border-white bg-white/90 backdrop-blur-sm shadow-xl transition-all duration-500 group-hover:shadow-blue-200/50 group-hover:scale-105">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <Image
                      src={getFileRenderUrl(school.brand.logo)}
                      alt={`${school.name} Logo`}
                      width={80}
                      height={80}
                      className="object-contain w-full h-full p-2 relative z-10"
                    />
                  </div>
                ) : (
                  <div 
                    className="relative w-20 h-20 rounded-2xl flex items-center justify-center text-white font-bold text-2xl shadow-xl overflow-hidden transition-all duration-500 group-hover:shadow-blue-200/50 group-hover:scale-105"
                    style={{ backgroundColor: school.brand?.color || '#3B82F6' }}
                  >
                    {/* Animated Background */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="absolute -bottom-6 -right-6 w-12 h-12 bg-white/10 rounded-full blur-xl"></div>
                    <div className="absolute -top-6 -left-6 w-12 h-12 bg-white/10 rounded-full blur-xl"></div>
                    
                    <span className="relative z-10">{school.name.charAt(0).toUpperCase()}</span>
                  </div>
                )}
              </div>

              {/* Enhanced School Name with AI-Style Effects */}
              <div className="flex items-center gap-4">
                <div className="relative">
                  <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-700">{school.name}</h1>
                  <div className="absolute -bottom-2 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500/80 to-indigo-500/80 rounded-full opacity-70"></div>
                </div>
                
                {/* Enhanced Edit Button */}
                {onEdit ? (
                  <button
                    onClick={handleEditClick}
                    className="relative p-3 text-blue-500 hover:text-blue-600 bg-blue-50/80 hover:bg-blue-100/80 backdrop-blur-sm rounded-full transition-all duration-300 cursor-pointer group overflow-hidden"
                    title="Edit School"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-200/30 to-indigo-200/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <Edit size={18} className="relative z-10" />
                    <span className="absolute inset-0 bg-blue-200/20 scale-0 group-hover:scale-100 rounded-full transition-transform duration-300"></span>
                  </button>
                ) : (
                  <Link
                    href="#"
                    className="relative p-3 text-blue-500 hover:text-blue-600 bg-blue-50/80 hover:bg-blue-100/80 backdrop-blur-sm rounded-full transition-all duration-300 cursor-pointer group overflow-hidden"
                    title="Edit School"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-200/30 to-indigo-200/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <Edit size={18} className="relative z-10" />
                    <span className="absolute inset-0 bg-blue-200/20 scale-0 group-hover:scale-100 rounded-full transition-transform duration-300"></span>
                  </Link>
                )}
              </div>
            </div>

            {/* Enhanced Delete Button */}
            {canDelete && onDelete && (
              <div className="relative group">
                <div className="absolute -inset-1 bg-gradient-to-r from-red-400/20 to-orange-400/20 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <Button
                  variant="error"
                  onClick={handleDeleteClick}
                  className="relative inline-flex items-center gap-3 text-sm bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-4 py-3 font-medium rounded-xl overflow-hidden transition-all duration-300"
                  disabled={isDeleting || showDeleteConfirm}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-red-500/20 to-orange-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <Trash2 size={16} className="relative z-10" />
                  <span className="relative z-10 font-semibold">Delete School</span>
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* AI-Enhanced Information Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Enhanced Contact Information */}
          <div className="relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-blue-100/50 to-indigo-100/50 rounded-2xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="relative bg-gradient-to-br from-blue-50/80 to-indigo-50/60 backdrop-blur-sm rounded-2xl p-6 border border-white/50 shadow-lg hover:shadow-xl transition-all duration-500">
              {/* Floating Elements */}
              <div className="absolute top-2 right-2 w-2 h-2 bg-blue-400/30 rounded-full animate-pulse"></div>
              <div className="absolute bottom-2 left-2 w-1 h-1 bg-indigo-400/40 rounded-full animate-ping"></div>
              
              <h3 className="text-base font-bold text-gray-800 mb-4 flex items-center gap-3">
                <div className="relative">
                  <Info size={18} className="text-blue-600" />
                  <div className="absolute inset-0 bg-blue-400/20 rounded-full blur-md animate-pulse"></div>
                </div>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">Contact Information</span>
              </h3>
              <div className="space-y-5">
                {school.phoneNumber && (
                  <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-xl border border-purple-100/50 hover:border-purple-200/70 transition-all duration-300 group">
                    <div className="relative">
                      <Phone size={16} className="text-purple-600 flex-shrink-0" />
                      <div className="absolute inset-0 bg-purple-400/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <a 
                      href={`tel:${school.phoneNumber}`}
                      className="text-sm font-medium text-gray-700 hover:text-purple-600 transition-colors flex-1"
                    >
                      {formatPhoneNumber(school.phoneNumber)}
                    </a>
                  </div>
                )}
                {school.email && (
                  <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-xl border border-green-100/50 hover:border-green-200/70 transition-all duration-300 group">
                    <div className="relative">
                      <Mail size={16} className="text-green-600 flex-shrink-0" />
                      <div className="absolute inset-0 bg-green-400/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <a 
                      href={`mailto:${school.email}`}
                      className="text-sm font-medium text-gray-700 hover:text-green-600 transition-colors break-all flex-1"
                    >
                      {school.email}
                    </a>
                  </div>
                )}
                {school.address && (
                  <div className="flex items-start gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-xl border border-blue-100/50 hover:border-blue-200/70 transition-all duration-300 group">
                    <div className="relative mt-0.5">
                      <MapPin size={16} className="text-blue-600 flex-shrink-0" />
                      <div className="absolute inset-0 bg-blue-400/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <a 
                      href={`https://maps.google.com/?q=${encodeURIComponent(school.address)}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors flex items-start gap-2 flex-1"
                    >
                      <span>{school.address}</span>
                      <ExternalLink size={12} className="opacity-60 flex-shrink-0 mt-0.5" />
                    </a>
                  </div>
                )}
                {school.admin && (
                  <div className="flex items-start gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-xl border border-indigo-100/50 hover:border-indigo-200/70 transition-all duration-300 group">
                    <div className="relative mt-0.5">
                      <UserCircle size={16} className="text-indigo-600 flex-shrink-0" />
                      <div className="absolute inset-0 bg-indigo-400/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <div className="text-sm text-gray-700 flex-1">
                      <p className="font-semibold text-gray-800">{school.admin.name}</p>
                      {school.admin.email && (
                        <a 
                          href={`mailto:${school.admin.email}`}
                          className="text-xs text-gray-600 hover:text-indigo-600 transition-colors font-medium"
                        >
                          {school.admin.email}
                        </a>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Brand & Additional Info */}
          <div className="space-y-4">
            {/* Brand Information */}
            {school.brand && (
              <div className="relative bg-white/60 backdrop-blur-sm rounded-xl p-5 border border-purple-100/50 overflow-hidden group">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-pink-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <h3 className="flex items-center gap-2 text-sm font-semibold mb-4">
                    <div className="relative">
                      <Palette size={16} className="text-purple-600" />
                      <div className="absolute inset-0 bg-purple-400/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-700 to-pink-700">Brand Identity</span>
                  </h3>
                  <div className="flex items-center gap-4 p-3 bg-white/70 backdrop-blur-sm rounded-lg border border-purple-100/30">
                    {school.brand.color && (
                      <div className="relative">
                        <div 
                          className="w-10 h-10 rounded-xl border-2 border-white shadow-lg flex-shrink-0 transition-transform duration-300 group-hover:scale-110"
                          style={{ backgroundColor: school.brand.color }}
                          title={`Brand Color: ${school.brand.color}`}
                        />
                        <div className="absolute inset-0 rounded-xl opacity-30 blur-md" style={{ backgroundColor: school.brand.color }}></div>
                      </div>
                    )}
                    <div>
                      {school.brand.color && (
                        <div>
                          <p className="text-xs text-gray-500 font-medium">Primary Color</p>
                          <span className="text-sm font-mono font-semibold text-gray-700">
                            {school.brand.color}
                          </span>
                        </div>
                      )}
                    </div>
                    {school.brand.image && (
                      <div className="relative w-12 h-8 border border-gray-200 rounded overflow-hidden bg-white ml-auto">
                        <Image
                          src={getFileRenderUrl(school.brand.image)}
                          alt="Brand"
                          width={48}
                          height={32}
                          className="object-cover w-full h-full"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Quick Stats */}
            <div className="relative bg-white/60 backdrop-blur-sm rounded-xl p-5 border border-green-100/50 overflow-hidden group">
              <div className="absolute inset-0 bg-gradient-to-br from-green-50/50 to-emerald-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <h3 className="flex items-center gap-2 text-sm font-semibold mb-5">
                  <div className="relative">
                    <Award size={16} className="text-green-600" />
                    <div className="absolute inset-0 bg-green-400/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-700 to-emerald-700">Analytics Overview</span>
                </h3>
                <div className="grid grid-cols-3 gap-3">
                  <div className="text-center p-3 bg-white/70 backdrop-blur-sm rounded-lg border border-blue-100/30 hover:border-blue-200/50 transition-all duration-300 group/stat">
                    <div className="relative mb-2">
                      <Users size={22} className="text-blue-600 mx-auto" />
                      <div className="absolute inset-0 bg-blue-400/20 rounded-full blur-sm opacity-0 group-hover/stat:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <p className="text-xs text-gray-500 font-medium mb-1">Teachers</p>
                    <p className="text-lg font-bold text-gray-800">-</p>
                  </div>
                  <div className="text-center p-3 bg-white/70 backdrop-blur-sm rounded-lg border border-purple-100/30 hover:border-purple-200/50 transition-all duration-300 group/stat">
                    <div className="relative mb-2">
                      <GraduationCap size={22} className="text-purple-600 mx-auto" />
                      <div className="absolute inset-0 bg-purple-400/20 rounded-full blur-sm opacity-0 group-hover/stat:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <p className="text-xs text-gray-500 font-medium mb-1">Students</p>
                    <p className="text-lg font-bold text-gray-800">-</p>
                  </div>
                  <div className="text-center p-3 bg-white/70 backdrop-blur-sm rounded-lg border border-orange-100/30 hover:border-orange-200/50 transition-all duration-300 group/stat">
                    <div className="relative mb-2">
                      <BookOpen size={22} className="text-orange-600 mx-auto" />
                      <div className="absolute inset-0 bg-orange-400/20 rounded-full blur-sm opacity-0 group-hover/stat:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <p className="text-xs text-gray-500 font-medium mb-1">Worksheets</p>
                    <p className="text-lg font-bold text-gray-800">-</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
